import { createInertiaApp } from "@inertiajs/react";
import { renderToString } from "react-dom/server";
import { route } from "ziggy-js";
import Layout from "./Layout";
import AdminLayout from "./AdminLayout";

export default function render(page) {
    return createInertiaApp({
        page,
        render: renderToString,
        resolve: (name) => {
            const pages = import.meta.glob("./pages/**/*.jsx", { eager: true });
            const page = pages[`./pages/${name}.jsx`];

            // Determine if this is an admin route
            const isAdminRoute = name.startsWith("Admin/");

            // Set the appropriate layout based on the route
            page.default.layout =
                page.default.layout ||
                ((page) => {
                    if (isAdminRoute) {
                        return <AdminLayout>{page}</AdminLayout>;
                    } else {
                        return <Layout>{page}</Layout>;
                    }
                });

            return page;
        },
        setup: ({ App, props }) => {
            // Make Ziggy routes available globally for SSR
            if (typeof global !== "undefined") {
                global.route = route;
                global.Ziggy = props.initialPage.props.ziggy;
            }
            return <App {...props} />;
        },
    });
}
