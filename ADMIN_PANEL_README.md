# Panda Patronage Admin Panel

## Overview

A comprehensive admin panel has been implemented for the Panda Patronage Laravel project with the following features:

- **Admin Authentication System** - Separate admin login with role-based access
- **Blog Management** - Full CRUD operations for articles with image upload
- **Cases/Portfolio Management** - Complete portfolio case management with multiple image support
- **Contact Queries Management** - View, search, filter, and export contact form submissions
- **Responsive Admin Interface** - Modern, user-friendly admin dashboard

## Admin Access

### Default Admin Credentials
- **Email**: <EMAIL>
- **Password**: admin123

### Admin Login URL
- **URL**: `/admin/login`
- **Full URL**: `http://your-domain.com/admin/login`

## Features

### 1. Authentication & Security
- Separate admin authentication system (removed regular user auth)
- Admin middleware protection for all admin routes
- Role-based access control with `is_admin` field
- Secure login/logout functionality

### 2. Dashboard
- Overview statistics (articles, cases, contact queries)
- Recent contact queries display
- Quick navigation to all admin sections

### 3. Blog Management (`/admin/articles`)
- **Create Articles**: Rich content editor with multiple content blocks
- **Edit Articles**: Update existing articles with image replacement
- **Delete Articles**: Remove articles with automatic image cleanup
- **Image Upload**: Local image storage with validation
- **Content Types**: Support for paragraphs, headings, lists, and quotes

### 4. Cases/Portfolio Management (`/admin/cases`)
- **Create Cases**: Add new portfolio cases with multiple images
- **Edit Cases**: Update case information and images
- **Delete Cases**: Remove cases with automatic image cleanup
- **Image Management**: Multiple image upload with thumbnail selection
- **Logo Upload**: Company logo support
- **Tags System**: Flexible tagging for categorization
- **Publication Status**: Draft/Published toggle

### 5. Contact Queries Management (`/admin/contact-queries`)
- **View All Queries**: Paginated list of all contact form submissions
- **Search & Filter**: Search by name, email, company, or message
- **Date Filtering**: Filter queries by date range
- **Excel Export**: Export all queries to .xlsx format
- **Individual Delete**: Remove specific queries
- **Bulk Delete**: Select and delete multiple queries
- **Detailed View**: Full contact information and message display

## Technical Implementation

### Database Schema

#### Users Table (Modified)
- Added `is_admin` boolean field for admin role identification

#### Articles Table (Existing)
- `slug`, `title`, `description`, `tag`, `content` (JSON), `image`, `date`, `read_time`

#### Cases Table (New)
- `slug`, `title`, `description`, `company_name`, `logo`, `images` (JSON), `thumbnail`, `tags` (JSON), `is_published`

#### Contact Queries Table (New)
- `first_name`, `last_name`, `company_name`, `phone_number`, `email`, `annual_revenue`, `message`

### File Structure

```
app/
├── Http/Controllers/Admin/
│   ├── AuthController.php          # Admin authentication
│   ├── DashboardController.php     # Admin dashboard
│   ├── ArticleController.php       # Blog management
│   ├── CaseController.php          # Cases management
│   └── ContactQueryController.php  # Contact queries management
├── Http/Middleware/
│   └── AdminMiddleware.php         # Admin access protection
├── Models/
│   ├── User.php                    # Updated with admin functionality
│   ├── Article.php                 # Existing blog model
│   ├── PortfolioCase.php          # New cases model
│   └── ContactQuery.php           # New contact queries model
└── Exports/
    └── ContactQueriesExport.php    # Excel export functionality

resources/js/
├── AdminLayout.jsx                 # Admin panel layout
├── pages/Admin/
│   ├── Login.jsx                   # Admin login page
│   ├── Dashboard.jsx               # Admin dashboard
│   ├── Articles/
│   │   ├── Index.jsx              # Articles listing
│   │   ├── Create.jsx             # Create article form
│   │   └── Edit.jsx               # Edit article form
│   ├── Cases/
│   │   ├── Index.jsx              # Cases listing
│   │   └── Create.jsx             # Create case form
│   └── ContactQueries/
│       └── Index.jsx              # Contact queries management
```

### Image Upload System
- **Storage Location**: `storage/app/public/`
- **Articles**: `storage/app/public/articles/`
- **Cases**: `storage/app/public/cases/images/` and `storage/app/public/cases/logos/`
- **Public Access**: Symlinked to `public/storage/`
- **Validation**: File type, size, and security checks
- **Automatic Cleanup**: Old images deleted when updated/removed

### Routes
- **Admin Routes**: All prefixed with `/admin`
- **Authentication**: `/admin/login`, `/admin/logout`
- **Dashboard**: `/admin`
- **Articles**: `/admin/articles/*`
- **Cases**: `/admin/cases/*`
- **Contact Queries**: `/admin/contact-queries`

## Integration with Existing System

### SEO Compatibility
- All existing SEO functionality preserved
- Public pages remain unaffected
- Dynamic content integration for cases and blog

### Public Page Updates
- **Cases Page**: Now displays dynamic content from database
- **Blog Page**: Already using dynamic content
- **Contact Form**: Now saves submissions to database

### Backward Compatibility
- All existing routes and functionality preserved
- No breaking changes to public-facing features
- Existing image URLs still supported during transition

## Installation & Setup

1. **Run Migrations**:
   ```bash
   php artisan migrate
   ```

2. **Create Storage Link**:
   ```bash
   php artisan storage:link
   ```

3. **Seed Admin User**:
   ```bash
   php artisan db:seed --class=AdminUserSeeder
   ```

4. **Install Dependencies** (if not already done):
   ```bash
   npm install @heroicons/react
   composer require maatwebsite/excel
   ```

5. **Create Storage Directories**:
   ```bash
   mkdir -p storage/app/public/articles
   mkdir -p storage/app/public/cases/logos
   mkdir -p storage/app/public/cases/images
   ```

## Usage Instructions

1. **Access Admin Panel**: Navigate to `/admin/login`
2. **Login**: Use admin credentials
3. **Manage Content**: Use the navigation to access different sections
4. **Upload Images**: Use the image upload fields in forms
5. **Export Data**: Use the export button in contact queries section

## Security Features

- Admin middleware protection
- CSRF token validation
- File upload validation
- Image type and size restrictions
- Secure file storage
- Role-based access control

## Future Enhancements

- User management for creating additional admin users
- Advanced content editor with WYSIWYG
- Image optimization and resizing
- Bulk operations for articles and cases
- Advanced analytics and reporting
- Email notifications for new contact queries

## Support

For any issues or questions regarding the admin panel, please refer to the Laravel documentation or contact the development team.
