<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        // Share current route name with Inertia for better active link detection
        \Inertia\Inertia::share([
            'currentRouteName' => fn () => \Illuminate\Support\Facades\Route::currentRouteName(),
        ]);
    }
}
