<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PortfolioTag;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PortfolioTagController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tags = PortfolioTag::orderBy('name')->paginate(15);

        return Inertia::render('Admin/PortfolioTags/Index', [
            'tags' => $tags,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/PortfolioTags/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:portfolio_tags,name',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        PortfolioTag::create($validated);

        return redirect()->route('admin.portfolio-tags.index')
            ->with('success', 'Portfolio tag created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PortfolioTag $portfolioTag)
    {
        return Inertia::render('Admin/PortfolioTags/Show', [
            'tag' => $portfolioTag->load('portfolioCases'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PortfolioTag $portfolioTag)
    {
        return Inertia::render('Admin/PortfolioTags/Edit', [
            'tag' => $portfolioTag,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PortfolioTag $portfolioTag)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:portfolio_tags,name,' . $portfolioTag->id,
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $portfolioTag->update($validated);

        return redirect()->route('admin.portfolio-tags.index')
            ->with('success', 'Portfolio tag updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PortfolioTag $portfolioTag)
    {
        $portfolioTag->delete();

        return redirect()->route('admin.portfolio-tags.index')
            ->with('success', 'Portfolio tag deleted successfully.');
    }
}
