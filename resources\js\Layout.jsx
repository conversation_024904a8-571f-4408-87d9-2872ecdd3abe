import DesktopHeader from "./components/DesktopHeader";
import MobileHeader from "./components/MobileHeader";
import Footer from "./components/Footer";
import SkipLink from "./components/SkipLink";
import { usePage } from "@inertiajs/react";
import { useEffect } from "react";
import { navLinks, getActiveNavItem } from "./constants/navigation";

const Layout = ({ children }) => {
    const page = usePage();
    const { url, currentRouteName } = page.props;

    // Use route-based active detection instead of URL matching
    const activeLink = getActiveNavItem(currentRouteName);

    const isHome = currentRouteName === "home";

    useEffect(() => {
        window.scrollTo({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    }, [url]);

    return (
        <div
            className={`relative ${isHome ? "bg-[rgb(245,246,249)]" : "bg-white"}`}
        >
            <SkipLink />
            <DesktopHeader navLinks={navLinks} activeLink={activeLink} />
            <MobileHeader navLinks={navLinks} activeLink={activeLink} />
            <main id="main-content" role="main">
                {children}
            </main>
            <Footer />
        </div>
    );
};

export default Layout;
