import { route } from "ziggy-js";

/**
 * Centralized navigation configuration for consistent active link detection
 * across all navigation components (Desktop, Mobile, Footer)
 */
export const navLinks = [
    {
        name: "home",
        label: "Home",
        route: "home",
        href: () => route("home"),
    },
    {
        name: "cases",
        label: "Cases",
        route: "cases",
        href: () => route("cases"),
    },
    {
        name: "about",
        label: "About",
        route: "about",
        href: () => route("about"),
    },
    {
        name: "blog",
        label: "Blog",
        route: "blog",
        href: () => route("blog"),
    },
    {
        name: "contact",
        label: "Contact",
        route: "contact",
        href: () => route("contact"),
    },
];

/**
 * Determines the active navigation item based on current route name
 * Handles nested routes (e.g., blog.detail -> blog)
 * 
 * @param {string} currentRouteName - Current route name from Inertia
 * @returns {string|null} - Active navigation item name or null
 */
export const getActiveNavItem = (currentRouteName) => {
    if (!currentRouteName) return null;

    // Direct match first
    const directMatch = navLinks.find(link => link.route === currentRouteName);
    if (directMatch) return directMatch.name;

    // Handle nested routes (e.g., blog.detail -> blog)
    const nestedMatch = navLinks.find(link => 
        currentRouteName.startsWith(link.name + ".")
    );
    if (nestedMatch) return nestedMatch.name;

    return null;
};

/**
 * Checks if a navigation item is currently active
 * 
 * @param {string} itemName - Navigation item name to check
 * @param {string} currentRouteName - Current route name from Inertia
 * @returns {boolean} - Whether the item is active
 */
export const isNavItemActive = (itemName, currentRouteName) => {
    return getActiveNavItem(currentRouteName) === itemName;
};
