import { Link, usePage } from "@inertiajs/react";
import { route } from "ziggy-js";
import { isNavItemActive } from "../constants/navigation";

/**
 * Reusable navigation link component with automatic active state detection
 * Follows Inertia best practices for route-based navigation
 * 
 * @param {Object} props
 * @param {string} props.routeName - Route name to navigate to
 * @param {string} props.navItemName - Navigation item name for active detection
 * @param {string} props.href - Optional custom href (defaults to route(routeName))
 * @param {string} props.activeClass - CSS classes for active state
 * @param {string} props.inactiveClass - CSS classes for inactive state
 * @param {boolean} props.exact - Whether to use exact route matching (default: false)
 * @param {React.ReactNode} props.children - Link content
 * @param {Object} props.rest - Additional props passed to Link component
 */
const NavLink = ({
    routeName,
    navItemName,
    href,
    activeClass = "text-white font-medium",
    inactiveClass = "text-gray-300 hover:text-white transition-colors duration-200",
    exact = false,
    children,
    ...rest
}) => {
    const page = usePage();
    const currentRouteName = page.props.currentRouteName || "";
    
    // Determine if this nav item is active
    const isActive = exact 
        ? currentRouteName === routeName
        : isNavItemActive(navItemName || routeName, currentRouteName);

    // Generate href if not provided
    const linkHref = href || route(routeName);

    return (
        <Link
            href={linkHref}
            className={isActive ? activeClass : inactiveClass}
            {...rest}
        >
            {children}
        </Link>
    );
};

export default NavLink;
