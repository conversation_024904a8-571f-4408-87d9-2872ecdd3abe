<?php

if (!function_exists('seo_meta')) {
    /**
     * Generate SEO meta tags for Inertia responses
     */
    function seo_meta(array $meta = []): array
    {
        $seoService = app(\App\Services\SeoService::class);
        return $seoService->generateMeta($meta);
    }
}

if (!function_exists('seo_structured_data')) {
    /**
     * Generate structured data (JSON-LD)
     */
    function seo_structured_data(string $type, array $data = []): array
    {
        $seoService = app(\App\Services\SeoService::class);
        return $seoService->generateStructuredData($type, $data);
    }
}

if (!function_exists('seo_breadcrumbs')) {
    /**
     * Generate breadcrumb structured data
     */
    function seo_breadcrumbs(array $breadcrumbs): array
    {
        $seoService = app(\App\Services\SeoService::class);
        return $seoService->generateBreadcrumbs($breadcrumbs);
    }
}

if (!function_exists('seo_truncate')) {
    /**
     * Truncate description to SEO-friendly length
     */
    function seo_truncate(string $description, int $maxLength = 160): string
    {
        $seoService = app(\App\Services\SeoService::class);
        return $seoService->truncateDescription($description, $maxLength);
    }
}

if (!function_exists('seo_page_data')) {
    /**
     * Generate complete SEO data for a page
     */
    function seo_page_data(array $meta = [], array $structuredData = []): array
    {
        return [
            'meta' => seo_meta($meta),
            'structuredData' => $structuredData,
        ];
    }
}
