import { Link } from "@inertiajs/react";
import BlogDetailSection from "../sections/BlogDetail/BlogDetailSection";
import BlogGridSection from "../sections/Blogs/BlogGridSection";
import SeoHead from "../components/SeoHead";
import { route } from "ziggy-js";

export default function BlogDetailDynamic({
    article,
    seoData,
    latestArticles = [],
}) {
    if (!article) {
        return (
            <div className="flex min-h-[60vh] flex-col items-center justify-center">
                <h2 className="mb-4 text-2xl font-bold">Blog not found</h2>
                <Link href={route("blog")} className="text-blue-600 underline">
                    Back to all articles
                </Link>
            </div>
        );
    }

    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <BlogDetailSection article={article} />
            <BlogGridSection
                articles={latestArticles}
                title="Latest Articles"
                subtitle="Stay informed with the latest guides and news."
                className="mt-20 bg-white"
            />
        </>
    );
}
