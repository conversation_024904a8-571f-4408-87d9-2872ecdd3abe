{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build --ssr resources/js/ssr.jsx", "dev": "vite", "ssr": "node bootstrap/ssr/ssr.js", "con": "concurrently \"php artisan serve\" \"npm run dev\"", "con:ssr": "concurrently \"php artisan serve\" \"npm run dev\" \"npm run ssr\""}, "dependencies": {"@inertiajs/react": "^2.0.14", "@phosphor-icons/react": "^2.1.10", "@tinymce/tinymce-react": "^6.2.1", "@vitejs/plugin-react": "^4.6.0", "framer-motion": "^12.23.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "ziggy-js": "^2.5.3"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.0.0", "vite": "^7.0.0"}}