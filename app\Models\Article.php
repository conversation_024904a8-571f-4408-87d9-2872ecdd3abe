<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug',
        'image',
        'date',
        'tag',
        'title',
        'content',
        'writer',
        'read_time',
        'is_featured',
    ];

    protected $casts = [
        'content' => 'array',
        'writer' => 'array',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the blog tags associated with this article.
     */
    public function blogTags()
    {
        return $this->belongsToMany(BlogTag::class, 'article_blog_tag');
    }
}