<?php

namespace Database\Seeders;

use App\Models\Article;
use Illuminate\Database\Seeder;

class ArticlesSeeder extends Seeder
{
    public function run(): void
    {
        $articles = [
            [
                'slug' => 'the-ux-revolution-shaping-digital-experiences',
                'image' => '/images/articles/ux-revolution.png',
                'date' => 'Nov 24, 2023',
                'tag' => 'UX Design',
                'title' => 'The UX Revolution: Shaping Digital Experiences',
                'description' => 'User Experience (UX) design is at the forefront of creating digital products that users love.',
                'content' => [
                    "In today's fast-paced digital world, User Experience (UX) design is more important than ever. Companies are realizing that a seamless, intuitive, and delightful user experience is key to building customer loyalty and driving business success."
                ],
                'writer' => null,
                'read_time' => null,
            ],
            [
                'slug' => 'mastering-ui-design-trends-a-deep-dive',
                'image' => '/images/articles/mastering-ui-trends.png',
                'date' => 'Nov 24, 2023',
                'tag' => 'UI Design',
                'title' => 'Mastering UI Design Trends: A Deep Dive',
                'description' => 'User Interface (UI) design is an art that constantly evolves. To create digital experiences that captivate and engage.',
                'content' => [
                    "UI design trends are constantly evolving, shaping the way users interact with digital products. Staying updated with the latest trends is crucial for designers who want to create captivating and effective interfaces.",
                    "## The Rise of Minimalism\n\nMinimalist design continues to dominate UI trends. Clean layouts, ample whitespace, and simple color palettes help users focus on content and actions without distractions."
                ],
                'writer' => null,
                'read_time' => null,
            ],
            // Add all other articles from the original data...
        ];

        foreach ($articles as $articleData) {
            Article::create($articleData);
        }
    }
}