<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default SEO Configuration
    |--------------------------------------------------------------------------
    |
    | These values are used as defaults for SEO meta tags across the application
    |
    */

    'defaults' => [
        'title' => 'Panda Patronage - Digital Marketing & Web Development Agency',
        'title_separator' => ' | ',
        'description' => 'Transform your digital presence with Panda Patronage. Expert web development, digital marketing, and creative solutions to grow your business online.',
        'keywords' => 'web development, digital marketing, Laravel, React, SEO, social media marketing, web design',
        'author' => 'Panda Patronage',
        'robots' => 'index, follow',
        'canonical_base' => env('APP_URL', 'https://pandapatronage.com'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Open Graph Configuration
    |--------------------------------------------------------------------------
    */

    'og' => [
        'site_name' => 'Panda Patronage',
        'type' => 'website',
        'locale' => 'en_US',
        'default_image' => '/images/misc/og-default.jpg',
        'image_width' => 1200,
        'image_height' => 630,
    ],

    /*
    |--------------------------------------------------------------------------
    | Twitter Card Configuration
    |--------------------------------------------------------------------------
    */

    'twitter' => [
        'card' => 'summary_large_image',
        'site' => '@pandapatronage',
        'creator' => '@pandapatronage',
    ],

    /*
    |--------------------------------------------------------------------------
    | Company Information
    |--------------------------------------------------------------------------
    */

    'company' => [
        'name' => 'Panda Patronage',
        'legal_name' => 'Panda Patronage Inc.',
        'url' => env('APP_URL', 'https://pandapatronage.com'),
        'logo' => '/images/misc/logo.png',
        'description' => 'Digital marketing and web development agency specializing in Laravel, React, and modern web technologies.',
        
        'contact' => [
            'email' => '<EMAIL>',
            'phone' => '******-XXX-XXXX',
            'address' => [
                'street' => '110 Notre-Dame St W',
                'city' => 'Montreal',
                'region' => 'Quebec',
                'postal_code' => 'H2Y 1T2',
                'country' => 'Canada',
                'country_code' => 'CA',
            ],
        ],

        'social' => [
            'facebook' => 'https://facebook.com/pandapatronage',
            'twitter' => 'https://twitter.com/pandapatronage',
            'linkedin' => 'https://linkedin.com/company/pandapatronage',
            'instagram' => 'https://instagram.com/pandapatronage',
            'youtube' => 'https://youtube.com/@pandapatronage',
        ],

        'business' => [
            'founded' => '2020',
            'employees' => '10-50',
            'industry' => 'Digital Marketing',
            'services' => [
                'Web Development',
                'Digital Marketing',
                'SEO Services',
                'Social Media Management',
                'Graphic Design',
                'Branding',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Structured Data Configuration
    |--------------------------------------------------------------------------
    */

    'structured_data' => [
        'organization' => [
            'type' => 'Organization',
            'subtype' => 'LocalBusiness',
            'price_range' => '$$',
            'currencies_accepted' => 'CAD, USD',
            'payment_accepted' => 'Cash, Credit Card, PayPal',
        ],

        'website' => [
            'search_action' => true,
            'search_target' => '/search?q={search_term_string}',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Sitemap Configuration
    |--------------------------------------------------------------------------
    */

    'sitemap' => [
        'cache_duration' => 60 * 24, // 24 hours in minutes
        'static_pages' => [
            '/' => ['priority' => '1.0', 'changefreq' => 'weekly'],
            '/about' => ['priority' => '0.8', 'changefreq' => 'monthly'],
            '/cases' => ['priority' => '0.8', 'changefreq' => 'weekly'],
            '/blog' => ['priority' => '0.9', 'changefreq' => 'daily'],
            '/contact' => ['priority' => '0.7', 'changefreq' => 'monthly'],
            '/privacy-policy' => ['priority' => '0.3', 'changefreq' => 'yearly'],
            '/terms-of-use' => ['priority' => '0.3', 'changefreq' => 'yearly'],
            '/licensing' => ['priority' => '0.3', 'changefreq' => 'yearly'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance & Technical SEO
    |--------------------------------------------------------------------------
    */

    'technical' => [
        'preload_fonts' => [
            'https://fonts.googleapis.com/css2?family=Great+Vibes&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap',
            'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap',
        ],
        
        'dns_prefetch' => [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
        ],

        'preconnect' => [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
        ],
    ],
];
