<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogTag;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BlogTagController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tags = BlogTag::orderBy('name')->paginate(15);

        return Inertia::render('Admin/BlogTags/Index', [
            'tags' => $tags,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/BlogTags/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:blog_tags,name',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        BlogTag::create($validated);

        return redirect()->route('admin.blog-tags.index')
            ->with('success', 'Blog tag created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BlogTag $blogTag)
    {
        return Inertia::render('Admin/BlogTags/Show', [
            'tag' => $blogTag->load('articles'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogTag $blogTag)
    {
        return Inertia::render('Admin/BlogTags/Edit', [
            'tag' => $blogTag,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlogTag $blogTag)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:blog_tags,name,' . $blogTag->id,
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $blogTag->update($validated);

        return redirect()->route('admin.blog-tags.index')
            ->with('success', 'Blog tag updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlogTag $blogTag)
    {
        $blogTag->delete();

        return redirect()->route('admin.blog-tags.index')
            ->with('success', 'Blog tag deleted successfully.');
    }
}
