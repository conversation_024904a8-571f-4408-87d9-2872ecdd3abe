<?php

namespace App\Services;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SeoService
{
    protected array $config;

    public function __construct()
    {
        $this->config = config('seo');
    }

    protected function getDefaultMeta(): array
    {
        return [
            'title' => $this->config['defaults']['title'],
            'description' => $this->config['defaults']['description'],
            'keywords' => $this->config['defaults']['keywords'],
            'author' => $this->config['defaults']['author'],
            'robots' => $this->config['defaults']['robots'],
            'og:type' => $this->config['og']['type'],
            'og:site_name' => $this->config['og']['site_name'],
            'twitter:card' => $this->config['twitter']['card'],
            'twitter:site' => $this->config['twitter']['site'],
        ];
    }

    public function generateMeta(array $customMeta = []): array
    {
        $meta = array_merge($this->getDefaultMeta(), $customMeta);

        // Ensure title is properly formatted
        $defaultTitle = $this->config['defaults']['title'];
        $separator = $this->config['defaults']['title_separator'];

        if (!str_contains($meta['title'], 'Panda Patronage') && $meta['title'] !== $defaultTitle) {
            $meta['title'] .= $separator . 'Panda Patronage';
        }

        // Generate Open Graph tags
        $meta['og:title'] = $meta['og:title'] ?? $meta['title'];
        $meta['og:description'] = $meta['og:description'] ?? $meta['description'];
        $meta['og:url'] = $meta['og:url'] ?? URL::current();
        $meta['og:image'] = $meta['og:image'] ?? URL::to('/images/misc/og-default.jpg');

        // Generate Twitter Card tags
        $meta['twitter:title'] = $meta['twitter:title'] ?? $meta['title'];
        $meta['twitter:description'] = $meta['twitter:description'] ?? $meta['description'];
        $meta['twitter:image'] = $meta['twitter:image'] ?? $meta['og:image'];

        // Generate canonical URL
        $meta['canonical'] = $meta['canonical'] ?? URL::current();

        return $meta;
    }

    public function generateStructuredData(string $type, array $data = []): array
    {
        $baseStructure = [
            '@context' => 'https://schema.org',
            '@type' => $type,
        ];

        switch ($type) {
            case 'Organization':
                $company = $this->config['company'];
                return array_merge($baseStructure, [
                    'name' => $company['name'],
                    'legalName' => $company['legal_name'],
                    'url' => $company['url'],
                    'logo' => URL::to($company['logo']),
                    'description' => $company['description'],
                    'foundingDate' => $company['business']['founded'],
                    'numberOfEmployees' => $company['business']['employees'],
                    'address' => [
                        '@type' => 'PostalAddress',
                        'streetAddress' => $company['contact']['address']['street'],
                        'addressLocality' => $company['contact']['address']['city'],
                        'addressRegion' => $company['contact']['address']['region'],
                        'postalCode' => $company['contact']['address']['postal_code'],
                        'addressCountry' => $company['contact']['address']['country_code'],
                    ],
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'telephone' => $company['contact']['phone'],
                        'contactType' => 'customer service',
                        'email' => $company['contact']['email'],
                    ],
                    'sameAs' => array_values($company['social']),
                ], $data);

            case 'WebSite':
                $company = $this->config['company'];
                return array_merge($baseStructure, [
                    'name' => $company['name'],
                    'url' => $company['url'],
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => $company['url'] . $this->config['structured_data']['website']['search_target'],
                        'query-input' => 'required name=search_term_string',
                    ],
                ], $data);

            case 'Article':
                $company = $this->config['company'];
                return array_merge($baseStructure, [
                    'headline' => $data['title'] ?? '',
                    'description' => $data['description'] ?? '',
                    'image' => URL::to($data['image'] ?? $this->config['og']['default_image']),
                    'author' => [
                        '@type' => 'Organization',
                        'name' => $company['name'],
                    ],
                    'publisher' => [
                        '@type' => 'Organization',
                        'name' => $company['name'],
                        'logo' => [
                            '@type' => 'ImageObject',
                            'url' => URL::to($company['logo']),
                        ],
                    ],
                    'datePublished' => $data['datePublished'] ?? now()->toISOString(),
                    'dateModified' => $data['dateModified'] ?? now()->toISOString(),
                ], $data);

            case 'Service':
                $company = $this->config['company'];
                return array_merge($baseStructure, [
                    'name' => $data['name'] ?? '',
                    'description' => $data['description'] ?? '',
                    'provider' => [
                        '@type' => 'Organization',
                        'name' => $company['name'],
                    ],
                    'serviceType' => $data['serviceType'] ?? 'Digital Marketing',
                ], $data);

            default:
                return array_merge($baseStructure, $data);
        }
    }

    public function generateBreadcrumbs(array $breadcrumbs): array
    {
        $items = [];

        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'] ?? null,
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items,
        ];
    }

    public function truncateDescription(string $description, int $maxLength = 160): string
    {
        if (strlen($description) <= $maxLength) {
            return $description;
        }

        return Str::limit($description, $maxLength - 3, '...');
    }

    public function getCompanyInfo(): array
    {
        return $this->config['company'];
    }
}
