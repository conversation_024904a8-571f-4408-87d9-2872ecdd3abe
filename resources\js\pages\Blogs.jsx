import DiscoverSection from "../sections/Blogs/DiscoverSection";
import BlogGridSection from "../sections/Blogs/BlogGridSection";
import SeoHead from "../components/SeoHead";

const Blogs = ({ articles = [], seoData }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="bg-white">
                <DiscoverSection articles={articles} />
                <BlogGridSection articles={articles} />
            </div>
        </>
    );
};

export default Blogs;
