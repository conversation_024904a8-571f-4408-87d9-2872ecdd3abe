<?php

namespace App\Console\Commands;

use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate XML sitemap for SEO';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sitemap = $this->generateSitemap();

        File::put(public_path('sitemap.xml'), $sitemap);

        $this->info('Sitemap generated successfully at public/sitemap.xml');
    }

    private function generateSitemap(): string
    {
        $baseUrl = config('app.url');
        $now = now()->toISOString();

        $urls = [
            ['url' => $baseUrl, 'lastmod' => $now, 'changefreq' => 'weekly', 'priority' => '1.0'],
            ['url' => $baseUrl . '/about', 'lastmod' => $now, 'changefreq' => 'monthly', 'priority' => '0.8'],
            ['url' => $baseUrl . '/cases', 'lastmod' => $now, 'changefreq' => 'weekly', 'priority' => '0.8'],
            ['url' => $baseUrl . '/blog', 'lastmod' => $now, 'changefreq' => 'daily', 'priority' => '0.9'],
            ['url' => $baseUrl . '/contact', 'lastmod' => $now, 'changefreq' => 'monthly', 'priority' => '0.7'],
            ['url' => $baseUrl . '/privacy-policy', 'lastmod' => $now, 'changefreq' => 'yearly', 'priority' => '0.3'],
            ['url' => $baseUrl . '/terms-of-use', 'lastmod' => $now, 'changefreq' => 'yearly', 'priority' => '0.3'],
            ['url' => $baseUrl . '/licensing', 'lastmod' => $now, 'changefreq' => 'yearly', 'priority' => '0.3'],
        ];

        // Add blog articles
        $articles = Article::all();
        foreach ($articles as $article) {
            $urls[] = [
                'url' => $baseUrl . '/blog/' . $article->slug,
                'lastmod' => $article->updated_at->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.6'
            ];
        }

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>{$url['url']}</loc>\n";
            $xml .= "    <lastmod>{$url['lastmod']}</lastmod>\n";
            $xml .= "    <changefreq>{$url['changefreq']}</changefreq>\n";
            $xml .= "    <priority>{$url['priority']}</priority>\n";
            $xml .= "  </url>\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }
}
