import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import {
    PlusIcon,
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CheckCircleIcon,
    XCircleIcon,
} from "@phosphor-icons/react";

const Index = ({ cases }) => {
    const handleDelete = (portfolioCase) => {
        if (confirm("Are you sure you want to delete this case?")) {
            router.delete(`/admin/cases/${portfolioCase.slug}`);
        }
    };

    return (
        <>
            <Head title="Cases Management" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Cases
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Manage your portfolio cases
                        </p>
                    </div>
                    <Link
                        href="/admin/cases/create"
                        className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                    >
                        <PlusIcon className="mr-2 h-4 w-4" />
                        New Case
                    </Link>
                </div>

                {/* Cases Grid */}
                <div className="overflow-hidden bg-white shadow sm:rounded-md">
                    {cases.data && cases.data.length > 0 ? (
                        <div className="grid grid-cols-1 gap-6 p-6 sm:grid-cols-2 lg:grid-cols-3">
                            {cases.data.map((portfolioCase) => (
                                <div
                                    key={portfolioCase.id}
                                    className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm"
                                >
                                    <div className="aspect-w-16 aspect-h-9">
                                        <img
                                            src={
                                                portfolioCase.thumbnail ||
                                                (portfolioCase.images &&
                                                    portfolioCase.images[0]) ||
                                                "/images/placeholder.jpg"
                                            }
                                            alt={portfolioCase.title}
                                            className="h-48 w-full object-cover"
                                        />
                                    </div>
                                    <div className="p-4">
                                        <div className="mb-2 flex items-center justify-between">
                                            <h3 className="truncate text-lg font-medium text-gray-900">
                                                {portfolioCase.title}
                                            </h3>
                                            <div className="flex items-center">
                                                {portfolioCase.is_published ? (
                                                    <CheckCircleIcon
                                                        className="h-5 w-5 text-green-500"
                                                        title="Published"
                                                    />
                                                ) : (
                                                    <XCircleIcon
                                                        className="h-5 w-5 text-red-500"
                                                        title="Draft"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                        <p className="mb-2 text-sm text-gray-600">
                                            {portfolioCase.company_name}
                                        </p>
                                        {portfolioCase.description && (
                                            <p className="mb-3 line-clamp-2 text-sm text-gray-500">
                                                {portfolioCase.description.substring(
                                                    0,
                                                    100,
                                                )}
                                                ...
                                            </p>
                                        )}
                                        {/* Portfolio Tags */}
                                        {portfolioCase.portfolio_tags &&
                                            portfolioCase.portfolio_tags
                                                .length > 0 && (
                                                <div className="mb-2 flex flex-wrap gap-1">
                                                    {portfolioCase.portfolio_tags
                                                        .slice(0, 3)
                                                        .map((tag) => (
                                                            <span
                                                                key={tag.id}
                                                                className="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium text-white"
                                                                style={{
                                                                    backgroundColor:
                                                                        tag.color,
                                                                }}
                                                            >
                                                                {tag.name}
                                                            </span>
                                                        ))}
                                                    {portfolioCase
                                                        .portfolio_tags.length >
                                                        3 && (
                                                        <span className="text-xs text-gray-500">
                                                            +
                                                            {portfolioCase
                                                                .portfolio_tags
                                                                .length -
                                                                3}{" "}
                                                            more
                                                        </span>
                                                    )}
                                                </div>
                                            )}

                                        {/* Regular Tags */}
                                        {portfolioCase.tags &&
                                            portfolioCase.tags.length > 0 && (
                                                <div className="mb-3 flex flex-wrap gap-1">
                                                    {portfolioCase.tags
                                                        .slice(0, 3)
                                                        .map((tag, index) => (
                                                            <span
                                                                key={index}
                                                                className="inline-flex items-center rounded bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                                                            >
                                                                {tag}
                                                            </span>
                                                        ))}
                                                    {portfolioCase.tags.length >
                                                        3 && (
                                                        <span className="text-xs text-gray-500">
                                                            +
                                                            {portfolioCase.tags
                                                                .length -
                                                                3}{" "}
                                                            more
                                                        </span>
                                                    )}
                                                </div>
                                            )}
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <Link
                                                    href={`/cases/${portfolioCase.slug}`}
                                                    target="_blank"
                                                    className="text-gray-400 hover:text-gray-600"
                                                    title="View Case"
                                                >
                                                    <EyeIcon className="h-4 w-4" />
                                                </Link>
                                                <Link
                                                    href={`/admin/cases/${portfolioCase.slug}/edit`}
                                                    className="text-indigo-600 hover:text-indigo-900"
                                                    title="Edit Case"
                                                >
                                                    <PencilIcon className="h-4 w-4" />
                                                </Link>
                                                <button
                                                    onClick={() =>
                                                        handleDelete(
                                                            portfolioCase,
                                                        )
                                                    }
                                                    className="text-red-600 hover:text-red-900"
                                                    title="Delete Case"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            </div>
                                            <div className="text-xs text-gray-400">
                                                {portfolioCase.images
                                                    ? portfolioCase.images
                                                          .length
                                                    : 0}{" "}
                                                images
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 text-center">
                            <p className="text-gray-500">No cases found</p>
                            <Link
                                href="/admin/cases/create"
                                className="mt-4 inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
                            >
                                <PlusIcon className="mr-2 h-4 w-4" />
                                Create your first case
                            </Link>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {cases.links && cases.links.length > 3 && (
                    <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                        <div className="flex flex-1 justify-between sm:hidden">
                            {cases.prev_page_url && (
                                <Link
                                    href={cases.prev_page_url}
                                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Previous
                                </Link>
                            )}
                            {cases.next_page_url && (
                                <Link
                                    href={cases.next_page_url}
                                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Next
                                </Link>
                            )}
                        </div>
                        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing{" "}
                                    <span className="font-medium">
                                        {cases.from}
                                    </span>{" "}
                                    to{" "}
                                    <span className="font-medium">
                                        {cases.to}
                                    </span>{" "}
                                    of{" "}
                                    <span className="font-medium">
                                        {cases.total}
                                    </span>{" "}
                                    results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm">
                                    {cases.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || "#"}
                                            className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${
                                                link.active
                                                    ? "z-10 border-indigo-500 bg-indigo-50 text-indigo-600"
                                                    : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50"
                                            } ${
                                                index === 0
                                                    ? "rounded-l-md"
                                                    : ""
                                            } ${
                                                index === cases.links.length - 1
                                                    ? "rounded-r-md"
                                                    : ""
                                            }`}
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    ))}
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

export default Index;
