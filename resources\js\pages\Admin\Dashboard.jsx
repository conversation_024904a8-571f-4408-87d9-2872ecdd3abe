import { Head } from "@inertiajs/react";
import { ArticleIcon, BriefcaseIcon, ChatCircleIcon, ClockIcon } from "@phosphor-icons/react";

const Dashboard = ({ stats }) => {
    const statCards = [
        {
            name: "Total Articles",
            value: stats.articles,
            icon: ArticleIcon,
            color: "bg-blue-500",
        },
        {
            name: "Total Cases",
            value: stats.cases,
            icon: BriefcaseIcon,
            color: "bg-green-500",
        },
        {
            name: "Contact Queries",
            value: stats.contact_queries,
            icon: ChatCircleIcon,
            color: "bg-yellow-500",
        },
    ];

    return (
        <>
            <Head title="Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Dashboard
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Welcome to your admin panel. Here's an overview of your
                        content.
                    </p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                    {statCards.map((stat) => (
                        <div
                            key={stat.name}
                            className="relative overflow-hidden rounded-lg bg-white px-4 pt-5 pb-12 shadow sm:px-6 sm:pt-6"
                        >
                            <dt>
                                <div
                                    className={`absolute ${stat.color} rounded-md p-3`}
                                >
                                    <stat.icon
                                        className="h-6 w-6 text-white"
                                        aria-hidden="true"
                                    />
                                </div>
                                <p className="ml-16 truncate text-sm font-medium text-gray-500">
                                    {stat.name}
                                </p>
                            </dt>
                            <dd className="ml-16 flex items-baseline pb-6 sm:pb-7">
                                <p className="text-2xl font-semibold text-gray-900">
                                    {stat.value}
                                </p>
                            </dd>
                        </div>
                    ))}
                </div>

                {/* Recent Contact Queries */}
                <div className="rounded-lg bg-white shadow">
                    <div className="px-4 py-5 sm:p-6">
                        <h3 className="mb-4 text-lg leading-6 font-medium text-gray-900">
                            Recent Contact Queries
                        </h3>
                        {stats.recent_queries &&
                        stats.recent_queries.length > 0 ? (
                            <div className="flow-root">
                                <ul className="-my-5 divide-y divide-gray-200">
                                    {stats.recent_queries.map((query) => (
                                        <li key={query.id} className="py-4">
                                            <div className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                                                        <span className="text-sm font-medium text-gray-700">
                                                            {query.first_name.charAt(
                                                                0,
                                                            )}
                                                            {query.last_name.charAt(
                                                                0,
                                                            )}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="truncate text-sm font-medium text-gray-900">
                                                        {query.first_name}{" "}
                                                        {query.last_name}
                                                    </p>
                                                    <p className="truncate text-sm text-gray-500">
                                                        {query.email}
                                                    </p>
                                                    {query.company_name && (
                                                        <p className="truncate text-sm text-gray-500">
                                                            {query.company_name}
                                                        </p>
                                                    )}
                                                </div>
                                                <div className="flex-shrink-0 text-sm text-gray-500">
                                                    <div className="flex items-center">
                                                        <ClockIcon className="mr-1 h-4 w-4" />
                                                        {new Date(
                                                            query.created_at,
                                                        ).toLocaleDateString()}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="mt-2">
                                                <p className="line-clamp-2 text-sm text-gray-600">
                                                    {query.message}
                                                </p>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ) : (
                            <p className="py-4 text-center text-gray-500">
                                No recent contact queries
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};

export default Dashboard;
