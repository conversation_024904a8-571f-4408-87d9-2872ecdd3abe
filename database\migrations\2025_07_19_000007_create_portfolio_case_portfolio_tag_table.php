<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_case_portfolio_tag', function (Blueprint $table) {
            $table->id();
            $table->foreignId('portfolio_case_id')->constrained('cases')->onDelete('cascade');
            $table->foreignId('portfolio_tag_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['portfolio_case_id', 'portfolio_tag_id'], 'case_tag_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_case_portfolio_tag');
    }
};
