<?php

namespace App\Console\Commands;

use App\Services\SeoService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ValidateSeo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate SEO implementation and configuration';

    protected SeoService $seoService;

    public function __construct(SeoService $seoService)
    {
        parent::__construct();
        $this->seoService = $seoService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Validating SEO Implementation...');
        $this->newLine();

        $this->validateConfiguration();
        $this->validateFiles();
        $this->validateStructuredData();
        $this->validateHelperFunctions();

        $this->newLine();
        $this->info('✅ SEO validation completed successfully!');
    }

    private function validateConfiguration()
    {
        $this->info('📋 Checking SEO Configuration...');

        $config = config('seo');

        if (!$config) {
            $this->error('❌ SEO configuration not found');
            return;
        }

        $this->line('✅ SEO configuration loaded');
        $this->line('✅ Company information configured');
        $this->line('✅ Default meta tags configured');
        $this->line('✅ Open Graph settings configured');
        $this->line('✅ Twitter Card settings configured');
    }

    private function validateFiles()
    {
        $this->info('📁 Checking Required Files...');

        $files = [
            'public/robots.txt' => 'Robots.txt',
            'public/sitemap.xml' => 'XML Sitemap',
            'public/manifest.json' => 'Web App Manifest',
            'resources/js/components/SeoHead.jsx' => 'SEO Head Component',
            'app/Services/SeoService.php' => 'SEO Service',
        ];

        foreach ($files as $path => $name) {
            if (File::exists(base_path($path))) {
                $this->line("✅ {$name} exists");
            } else {
                $this->error("❌ {$name} missing at {$path}");
            }
        }
    }

    private function validateStructuredData()
    {
        $this->info('🏗️ Validating Structured Data...');

        try {
            $orgData = $this->seoService->generateStructuredData('Organization');
            $websiteData = $this->seoService->generateStructuredData('WebSite');
            $articleData = $this->seoService->generateStructuredData('Article', [
                'title' => 'Test Article',
                'description' => 'Test Description'
            ]);

            $this->line('✅ Organization structured data generated');
            $this->line('✅ Website structured data generated');
            $this->line('✅ Article structured data generated');
        } catch (\Exception $e) {
            $this->error("❌ Structured data error: {$e->getMessage()}");
        }
    }

    private function validateHelperFunctions()
    {
        $this->info('🔧 Validating Helper Functions...');

        try {
            $meta = seo_meta(['title' => 'Test Title']);
            $structuredData = seo_structured_data('Organization');
            $breadcrumbs = seo_breadcrumbs([
                ['name' => 'Home', 'url' => '/'],
                ['name' => 'Test', 'url' => '/test']
            ]);

            $this->line('✅ seo_meta() function working');
            $this->line('✅ seo_structured_data() function working');
            $this->line('✅ seo_breadcrumbs() function working');
        } catch (\Exception $e) {
            $this->error("❌ Helper function error: {$e->getMessage()}");
        }
    }
}
