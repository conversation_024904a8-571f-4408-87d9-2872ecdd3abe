import React from "react";
import ProjectShowcase from "../components/ProjectShowcase";
import SeoHead from "../components/SeoHead";

const CaseDetail = ({ caseDetail = caseDetail, cases = [], seoData }) => {
    return (
        <React.Fragment>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="mx-auto flex min-h-screen w-full max-w-5xl flex-col items-center bg-white">
                {/* Content Container - matches project images width */}
                <div className="w-full max-w-6xl">
                    {/* Header - left aligned within project images width */}
                    <div className="flex w-fit items-center space-x-4 px-2 pb-6 lg:px-0">
                        {/* Logo circle */}
                        <div className="flex size-10 items-center justify-center rounded-[50%] bg-transparent shadow-lg">
                            <img
                                src={caseDetail.logo}
                                alt={caseDetail.companyName}
                                className="size-10 rounded-[50%] object-contain"
                            />
                        </div>

                        {/* Text block */}
                        <div>
                            <h1 className="text-base leading-none font-bold text-gray-900">
                                {caseDetail.companyName}
                            </h1>
                            <p className="mt-1.5 text-sm leading-none text-gray-600">
                                {caseDetail.projectName}
                            </p>
                        </div>
                    </div>

                    {/* Image Stack - no gaps */}
                    <div
                        className="flex flex-col"
                        style={{ fontSize: 0, lineHeight: 0 }}
                    >
                        {caseDetail.images.map((img, idx) => (
                            <img
                                key={idx}
                                src={img}
                                alt={`Case image ${idx + 1}`}
                                className="w-full object-contain"
                                style={{
                                    display: "block",
                                    margin: 0,
                                    padding: 0,
                                    border: 0,
                                    verticalAlign: "top",
                                }}
                            />
                        ))}
                    </div>
                </div>

                {/* More Cases Section */}
                <div className="mx-auto mt-12 w-full max-w-6xl">
                    <h2 className="mb-6 text-2xl font-bold text-[#2d2318]">
                        More cases
                    </h2>
                    <ProjectShowcase cases={cases} />
                </div>
            </div>
        </React.Fragment>
    );
};

export default CaseDetail;
