import { Head } from "@inertiajs/react";

const SeoHead = ({ 
    title,
    description,
    keywords,
    canonical,
    ogTitle,
    ogDescription,
    ogImage,
    ogUrl,
    ogType = "website",
    twitterCard = "summary_large_image",
    twitterTitle,
    twitterDescription,
    twitterImage,
    twitterSite = "@pandapatronage",
    robots = "index, follow",
    author = "Panda Patronage",
    structuredData = null,
    additionalMeta = {}
}) => {
    // Ensure title includes site name if not already present
    const formattedTitle = title && !title.includes('Panda Patronage') && title !== 'Panda Patronage - Digital Marketing & Web Development Agency'
        ? `${title} | Panda Patronage`
        : title || 'Panda Patronage - Digital Marketing & Web Development Agency';

    // Use provided values or fall back to main values
    const finalOgTitle = ogTitle || formattedTitle;
    const finalOgDescription = ogDescription || description;
    const finalTwitterTitle = twitterTitle || formattedTitle;
    const finalTwitterDescription = twitterDescription || description;
    const finalTwitterImage = twitterImage || ogImage;

    return (
        <Head>
            {/* Basic Meta Tags */}
            <title>{formattedTitle}</title>
            {description && <meta name="description" content={description} />}
            {keywords && <meta name="keywords" content={keywords} />}
            {author && <meta name="author" content={author} />}
            <meta name="robots" content={robots} />
            
            {/* Canonical URL */}
            {canonical && <link rel="canonical" href={canonical} />}
            
            {/* Open Graph Tags */}
            <meta property="og:title" content={finalOgTitle} />
            {finalOgDescription && <meta property="og:description" content={finalOgDescription} />}
            <meta property="og:type" content={ogType} />
            <meta property="og:site_name" content="Panda Patronage" />
            {ogUrl && <meta property="og:url" content={ogUrl} />}
            {ogImage && <meta property="og:image" content={ogImage} />}
            {ogImage && <meta property="og:image:alt" content={`${formattedTitle} - Panda Patronage`} />}
            
            {/* Twitter Card Tags */}
            <meta name="twitter:card" content={twitterCard} />
            <meta name="twitter:site" content={twitterSite} />
            <meta name="twitter:title" content={finalTwitterTitle} />
            {finalTwitterDescription && <meta name="twitter:description" content={finalTwitterDescription} />}
            {finalTwitterImage && <meta name="twitter:image" content={finalTwitterImage} />}
            
            {/* Additional Meta Tags */}
            {Object.entries(additionalMeta).map(([key, value]) => (
                <meta key={key} name={key} content={value} />
            ))}
            
            {/* Structured Data */}
            {structuredData && (
                <script type="application/ld+json">
                    {JSON.stringify(structuredData)}
                </script>
            )}
            
            {/* Favicon and App Icons */}
            <link rel="icon" type="image/x-icon" href="/images/misc/logo.png" />
            <link rel="apple-touch-icon" href="/images/misc/logo.png" />
            
            {/* Additional SEO Meta Tags */}
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="language" content="English" />
            <meta name="revisit-after" content="7 days" />
            <meta name="distribution" content="web" />
            <meta name="rating" content="general" />
        </Head>
    );
};

export default SeoHead;
