import { createInertiaApp } from "@inertiajs/react";
import { createRoot } from "react-dom/client";
import "../css/app.css";
import Layout from "./Layout";
import AdminLayout from "./AdminLayout";

createInertiaApp({
    resolve: (name) => {
        const pages = import.meta.glob("./pages/**/*.jsx", { eager: true });
        const page = pages[`./pages/${name}.jsx`];

        // Determine if this is an admin route
        const isAdminRoute = name.startsWith("Admin/");

        // Set the appropriate layout based on the route
        page.default.layout =
            page.default.layout ||
            ((page) => {
                if (isAdminRoute) {
                    return <AdminLayout>{page}</AdminLayout>;
                } else {
                    return <Layout>{page}</Layout>;
                }
            });

        return page;
    },
    setup({ el, App, props }) {
        createRoot(el).render(<App {...props} />);
    },
});
