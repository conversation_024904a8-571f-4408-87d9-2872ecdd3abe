import React from "react";
import ScrollReveal from "./ScrollReveal";

/**
 * Demo component to showcase the enhanced ScrollReveal functionality
 * This component demonstrates various use cases and configurations
 */
const ScrollRevealDemo = () => {
    return (
        <div className="min-h-screen bg-gray-50 py-16">
            <div className="mx-auto max-w-4xl px-4">
                <h1 className="mb-16 text-center text-4xl font-bold text-gray-900">
                    Enhanced ScrollReveal Demo
                </h1>

                {/* Basic Usage */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Basic Usage
                    </h2>
                    <ScrollReveal className="rounded-lg bg-white p-8 shadow-md">
                        <h3 className="mb-4 text-xl font-medium text-gray-900">
                            Default Animation
                        </h3>
                        <p className="text-gray-600">
                            This element uses the default ScrollReveal settings with
                            optimized performance and accessibility features.
                        </p>
                    </ScrollReveal>
                </section>

                {/* Custom Timing */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Custom Timing & Delays
                    </h2>
                    <div className="space-y-4">
                        <ScrollReveal
                            delay={0.1}
                            duration={0.6}
                            className="rounded-lg bg-blue-50 p-6 shadow-md"
                        >
                            <p className="text-blue-800">
                                Delayed by 0.1s, duration 0.6s
                            </p>
                        </ScrollReveal>
                        <ScrollReveal
                            delay={0.3}
                            duration={0.8}
                            className="rounded-lg bg-green-50 p-6 shadow-md"
                        >
                            <p className="text-green-800">
                                Delayed by 0.3s, duration 0.8s
                            </p>
                        </ScrollReveal>
                        <ScrollReveal
                            delay={0.5}
                            duration={1.0}
                            className="rounded-lg bg-purple-50 p-6 shadow-md"
                        >
                            <p className="text-purple-800">
                                Delayed by 0.5s, duration 1.0s
                            </p>
                        </ScrollReveal>
                    </div>
                </section>

                {/* Different Elements */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Different HTML Elements
                    </h2>
                    <div className="space-y-6">
                        <ScrollReveal
                            as="h3"
                            className="text-3xl font-bold text-gray-900"
                        >
                            Animated Heading (h3)
                        </ScrollReveal>
                        
                        <ScrollReveal
                            as="p"
                            className="text-lg text-gray-600"
                        >
                            This is an animated paragraph with enhanced performance
                            optimizations and accessibility support.
                        </ScrollReveal>
                        
                        <ScrollReveal
                            as="img"
                            src="/images/misc/logo.png"
                            alt="Animated Logo"
                            className="mx-auto h-16 w-32 object-contain"
                        />
                        
                        <ScrollReveal
                            as="button"
                            className="rounded-lg bg-blue-600 px-6 py-3 text-white hover:bg-blue-700"
                        >
                            Animated Button
                        </ScrollReveal>
                    </div>
                </section>

                {/* Custom Animation Distance */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Custom Animation Distance
                    </h2>
                    <div className="space-y-4">
                        <ScrollReveal
                            translateY={20}
                            className="rounded-lg bg-yellow-50 p-6 shadow-md"
                        >
                            <p className="text-yellow-800">
                                Small translation (20px)
                            </p>
                        </ScrollReveal>
                        <ScrollReveal
                            translateY={60}
                            className="rounded-lg bg-red-50 p-6 shadow-md"
                        >
                            <p className="text-red-800">
                                Large translation (60px)
                            </p>
                        </ScrollReveal>
                    </div>
                </section>

                {/* Trigger Once */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Trigger Once Mode
                    </h2>
                    <ScrollReveal
                        triggerOnce={true}
                        className="rounded-lg bg-indigo-50 p-8 shadow-md"
                    >
                        <h3 className="mb-4 text-xl font-medium text-indigo-900">
                            One-Time Animation
                        </h3>
                        <p className="text-indigo-700">
                            This element will only animate once when it first comes
                            into view. Scroll up and down to test this behavior.
                        </p>
                    </ScrollReveal>
                </section>

                {/* Custom Threshold */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Custom Intersection Threshold
                    </h2>
                    <ScrollReveal
                        threshold={0.5}
                        className="rounded-lg bg-teal-50 p-8 shadow-md"
                    >
                        <h3 className="mb-4 text-xl font-medium text-teal-900">
                            50% Visibility Required
                        </h3>
                        <p className="text-teal-700">
                            This element only animates when 50% of it is visible
                            in the viewport.
                        </p>
                    </ScrollReveal>
                </section>

                {/* Performance Test */}
                <section className="mb-16">
                    <h2 className="mb-8 text-2xl font-semibold text-gray-800">
                        Performance Test - Multiple Elements
                    </h2>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {Array.from({ length: 12 }, (_, i) => (
                            <ScrollReveal
                                key={i}
                                delay={i * 0.05}
                                className="rounded-lg bg-gray-100 p-4 shadow-sm"
                            >
                                <div className="text-center">
                                    <div className="mb-2 text-2xl font-bold text-gray-700">
                                        {i + 1}
                                    </div>
                                    <p className="text-sm text-gray-600">
                                        Optimized element #{i + 1}
                                    </p>
                                </div>
                            </ScrollReveal>
                        ))}
                    </div>
                </section>

                {/* Accessibility Note */}
                <section className="mb-16">
                    <ScrollReveal className="rounded-lg bg-amber-50 p-8 shadow-md border-l-4 border-amber-400">
                        <h3 className="mb-4 text-xl font-medium text-amber-900">
                            Accessibility Features
                        </h3>
                        <ul className="space-y-2 text-amber-800">
                            <li>• Respects prefers-reduced-motion settings</li>
                            <li>• Hardware-accelerated animations</li>
                            <li>• Efficient Intersection Observer usage</li>
                            <li>• Memory leak prevention</li>
                            <li>• Scroll direction detection</li>
                            <li>• Comprehensive error handling</li>
                        </ul>
                    </ScrollReveal>
                </section>

                {/* Footer spacer */}
                <div className="h-96"></div>
            </div>
        </div>
    );
};

export default ScrollRevealDemo;
