import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import { Plus, Pencil, Trash, Tag } from "@phosphor-icons/react";

const Index = ({ tags }) => {
    const handleDelete = (id) => {
        if (confirm("Are you sure you want to delete this tag?")) {
            router.delete(`/admin/portfolio-tags/${id}`);
        }
    };

    return (
        <>
            <Head title="Portfolio Tags" />

            <div className="min-h-screen bg-gray-50 py-6">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">
                                    Portfolio Tags
                                </h1>
                                <p className="mt-2 text-gray-600">
                                    Manage tags for your portfolio cases
                                </p>
                            </div>
                            <Link
                                href="/admin/portfolio-tags/create"
                                className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                <Plus className="mr-2 h-4 w-4" />
                                Create Tag
                            </Link>
                        </div>
                    </div>

                    {/* Tags Grid */}
                    <div className="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul className="divide-y divide-gray-200">
                            {tags.data.map((tag) => (
                                <li key={tag.id}>
                                    <div className="px-4 py-4 flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div
                                                className="flex-shrink-0 h-4 w-4 rounded-full"
                                                style={{ backgroundColor: tag.color }}
                                            ></div>
                                            <div className="ml-4">
                                                <div className="flex items-center">
                                                    <h3 className="text-sm font-medium text-gray-900">
                                                        {tag.name}
                                                    </h3>
                                                    {!tag.is_active && (
                                                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            Inactive
                                                        </span>
                                                    )}
                                                </div>
                                                {tag.description && (
                                                    <p className="text-sm text-gray-500">
                                                        {tag.description}
                                                    </p>
                                                )}
                                                <p className="text-xs text-gray-400">
                                                    Slug: {tag.slug}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Link
                                                href={`/admin/portfolio-tags/${tag.id}/edit`}
                                                className="text-indigo-600 hover:text-indigo-900"
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Link>
                                            <button
                                                onClick={() => handleDelete(tag.id)}
                                                className="text-red-600 hover:text-red-900"
                                            >
                                                <Trash className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>

                        {tags.data.length === 0 && (
                            <div className="text-center py-12">
                                <Tag className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">
                                    No tags
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Get started by creating a new portfolio tag.
                                </p>
                                <div className="mt-6">
                                    <Link
                                        href="/admin/portfolio-tags/create"
                                        className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Create Tag
                                    </Link>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Pagination */}
                    {tags.links && tags.links.length > 3 && (
                        <div className="mt-6 flex justify-center">
                            <nav className="flex space-x-2">
                                {tags.links.map((link, index) => (
                                    <Link
                                        key={index}
                                        href={link.url || "#"}
                                        className={`px-3 py-2 text-sm rounded-md ${
                                            link.active
                                                ? "bg-indigo-600 text-white"
                                                : "bg-white text-gray-700 hover:bg-gray-50"
                                        } ${!link.url ? "cursor-not-allowed opacity-50" : ""}`}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </nav>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default Index;
