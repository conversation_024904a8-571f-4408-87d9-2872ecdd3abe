import CasesIntro from "../sections/Cases/CasesIntro";
import ProjectShowcase from "../components/ProjectShowcase";
import FaqsSection from "../components/FaqsSection";
import SeoHead from "../components/SeoHead";

const Cases = ({ seoData, cases =[] }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <CasesIntro />
            <ProjectShowcase cases={cases} />
            <FaqsSection />
        </>
    );
};

export default Cases;
