import VendorSection from "../sections/About/VendorSection";
import DirectorSection from "../sections/About/DirectorSection";
import StatsSection from "../sections/About/StatsSection";
import TeamSection from "../sections/About/TeamSection";
import FaqsSection from "../components/FaqsSection";
import SeoHead from "../components/SeoHead";

const About = ({ seoData }) => {
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="px-4">
                <VendorSection />
                <DirectorSection />
                <StatsSection />
                <TeamSection />
                <FaqsSection />
            </div>
        </>
    );
};

export default About;
