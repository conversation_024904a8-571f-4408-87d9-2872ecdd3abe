import { motion, useAnimation } from "framer-motion";
import { useEffect, useRef, useCallback, useMemo } from "react";

/**
 * Enhanced ScrollReveal component with senior developer best practices
 *
 * Features:
 * - Efficient Intersection Observer API usage
 * - Scroll direction detection (down-only animations)
 * - Performance optimizations with debouncing and cleanup
 * - Accessibility support (prefers-reduced-motion)
 * - Hardware acceleration with CSS transforms
 * - Comprehensive error handling
 * - Memory leak prevention
 *
 * @param {React.ReactNode} children - Content to animate
 * @param {string} className - CSS classes to apply
 * @param {number} delay - Animation delay in seconds (default: 0)
 * @param {number} duration - Animation duration in seconds (default: 0.3)
 * @param {object} style - Inline styles
 * @param {string|React.ElementType} as - HTML element or React component (default: 'div')
 * @param {string} [alt] - Alt attribute for img elements
 * @param {string} [src] - Src attribute for img elements
 * @param {number} threshold - Intersection threshold (default: 0.15)
 * @param {string} rootMargin - Root margin for intersection observer (default: '0px')
 * @param {boolean} triggerOnce - Whether to trigger animation only once (default: false)
 * @param {object} customEasing - Custom easing function (default: cubic-bezier)
 * @param {number} translateY - Y-axis translation distance (default: 40)
 * @param {boolean} disabled - Disable animations (useful for testing/accessibility)
 */
export default function ScrollReveal({
    children,
    className = "",
    delay = 0,
    duration = 0.3,
    style = {},
    as = "div",
    alt,
    src,
    threshold = 0.15,
    rootMargin = "0px",
    triggerOnce = false,
    customEasing = [0.25, 0.46, 0.45, 0.94], // cubic-bezier for smooth easing
    translateY = 40,
    disabled = false,
}) {
    // Refs for performance and cleanup
    const elementRef = useRef(null);
    const observerRef = useRef(null);
    const lastScrollY = useRef(
        typeof window !== "undefined" ? window.scrollY : 0,
    );
    const animationFrameRef = useRef(null);
    const hasAnimated = useRef(false);
    const isScrollingDown = useRef(true);

    // Animation controls
    const controls = useAnimation();

    // Check for reduced motion preference
    const prefersReducedMotion = useMemo(() => {
        if (typeof window === "undefined") return false;
        return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
    }, []);

    // Debounced scroll handler for performance
    const debouncedScrollHandler = useCallback(() => {
        if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
        }

        animationFrameRef.current = requestAnimationFrame(() => {
            try {
                const currentScrollY = window.scrollY;
                isScrollingDown.current = currentScrollY > lastScrollY.current;
                lastScrollY.current = currentScrollY;
            } catch (error) {
                console.warn("ScrollReveal: Error in scroll handler:", error);
            }
        });
    }, []);

    // Intersection Observer callback
    const handleIntersection = useCallback(
        (entries) => {
            try {
                const [entry] = entries;
                const isIntersecting = entry.isIntersecting;

                // Only animate IN when:
                // 1. Element is intersecting
                // 2. User is scrolling down
                // 3. Animation hasn't been triggered yet (if triggerOnce is true)
                // 4. Animations are not disabled
                const shouldAnimateIn =
                    isIntersecting &&
                    isScrollingDown.current &&
                    (!triggerOnce || !hasAnimated.current) &&
                    !disabled;

                if (shouldAnimateIn) {
                    hasAnimated.current = true;

                    // Animate in with optimized properties
                    controls.start({
                        opacity: 1,
                        y: 0,
                        transition: {
                            duration: prefersReducedMotion ? 0 : duration,
                            delay: prefersReducedMotion ? 0 : delay,
                            ease: customEasing,
                        },
                    });
                } else if (
                    !isIntersecting &&
                    !triggerOnce &&
                    !hasAnimated.current
                ) {
                    // Only animate out if the element hasn't been animated in yet
                    // This prevents content from disappearing when scrolling up after scrolling down
                    controls.start({
                        opacity: 0,
                        y: translateY,
                        transition: {
                            duration: prefersReducedMotion ? 0 : duration * 0.5,
                            ease: customEasing,
                        },
                    });
                }
                // Note: Once an element has been animated in, it stays visible
                // even when scrolling up and out of view
            } catch (error) {
                console.warn(
                    "ScrollReveal: Error in intersection handler:",
                    error,
                );
            }
        },
        [
            controls,
            delay,
            duration,
            triggerOnce,
            customEasing,
            translateY,
            disabled,
            prefersReducedMotion,
        ],
    );

    // Initialize Intersection Observer
    useEffect(() => {
        if (!elementRef.current || typeof window === "undefined") return;

        try {
            // Create observer with optimized options
            observerRef.current = new IntersectionObserver(handleIntersection, {
                threshold,
                rootMargin,
                // Use passive observation for better performance
                passive: true,
            });

            observerRef.current.observe(elementRef.current);

            // Add scroll listener for direction detection
            window.addEventListener("scroll", debouncedScrollHandler, {
                passive: true,
            });

            // Cleanup function
            return () => {
                if (observerRef.current) {
                    observerRef.current.disconnect();
                }
                if (animationFrameRef.current) {
                    cancelAnimationFrame(animationFrameRef.current);
                }
                window.removeEventListener("scroll", debouncedScrollHandler);
            };
        } catch (error) {
            console.warn("ScrollReveal: Error initializing observer:", error);
        }
    }, [handleIntersection, threshold, rootMargin, debouncedScrollHandler]);

    // Memoized motion component to prevent unnecessary re-renders
    const MotionComponent = useMemo(() => {
        try {
            return motion[as] || motion.div;
        } catch (error) {
            console.warn(
                "ScrollReveal: Invalid motion component, falling back to div:",
                error,
            );
            return motion.div;
        }
    }, [as]);

    // Prepare extra props for specific elements
    const extraProps = useMemo(() => {
        const props = {};
        if (as === "img") {
            if (alt) props.alt = alt;
            if (src) props.src = src;
        }
        return props;
    }, [as, alt, src]);

    // Optimized styles with hardware acceleration
    const optimizedStyle = useMemo(
        () => ({
            ...style,
            // Enable hardware acceleration
            willChange: "transform, opacity",
            // Ensure proper layering
            transform: "translateZ(0)",
            ...style, // Allow style overrides
        }),
        [style],
    );

    // If animations are disabled or reduced motion is preferred, render without animation
    if (disabled || prefersReducedMotion) {
        const StaticComponent = as === "div" ? "div" : as;
        return (
            <StaticComponent
                ref={elementRef}
                className={className}
                style={optimizedStyle}
                {...extraProps}
            >
                {children}
            </StaticComponent>
        );
    }

    return (
        <MotionComponent
            ref={elementRef}
            initial={{
                opacity: 0,
                y: translateY,
                // Ensure transforms are GPU-accelerated
                transform: "translateZ(0)",
            }}
            animate={controls}
            className={className}
            style={optimizedStyle}
            {...extraProps}
        >
            {children}
        </MotionComponent>
    );
}
