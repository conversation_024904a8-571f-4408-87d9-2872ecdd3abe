import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import {
    PlusIcon,
    PencilIcon,
    TrashIcon,
    EyeIcon,
} from "@phosphor-icons/react";

const Index = ({ articles }) => {
    const handleDelete = (article) => {
        if (confirm("Are you sure you want to delete this article?")) {
            router.delete(`/admin/articles/${article.id}`);
        }
    };

    return (
        <>
            <Head title="Articles Management" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Articles
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Manage your blog articles
                        </p>
                    </div>
                    <Link
                        href="/admin/articles/create"
                        className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                    >
                        <PlusIcon className="mr-2 h-4 w-4" />
                        New Article
                    </Link>
                </div>

                {/* Articles Table */}
                <div className="overflow-hidden bg-white shadow sm:rounded-md">
                    {articles.data && articles.data.length > 0 ? (
                        <ul className="divide-y divide-gray-200">
                            {articles.data.map((article) => (
                                <li key={article.id}>
                                    <div className="flex items-center justify-between px-4 py-4">
                                        <div className="flex items-center">
                                            <div className="h-16 w-16 flex-shrink-0">
                                                <img
                                                    className="h-16 w-16 rounded-lg object-cover"
                                                    src={article.image}
                                                    alt={article.title}
                                                />
                                            </div>
                                            <div className="ml-4">
                                                <div className="flex items-center">
                                                    <p className="truncate text-sm font-medium text-gray-900">
                                                        {article.title}
                                                    </p>
                                                    <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                                        {article.tag}
                                                    </span>
                                                </div>
                                                <p className="mt-1 text-sm text-gray-500">
                                                    {typeof article.content ===
                                                    "string"
                                                        ? article.content
                                                              .replace(
                                                                  /<[^>]*>/g,
                                                                  "",
                                                              )
                                                              .substring(
                                                                  0,
                                                                  100,
                                                              ) + "..."
                                                        : Array.isArray(
                                                                article.content,
                                                            )
                                                          ? article.content
                                                                .map(
                                                                    (block) =>
                                                                        block.content,
                                                                )
                                                                .join(" ")
                                                                .substring(
                                                                    0,
                                                                    100,
                                                                ) + "..."
                                                          : "No content available"}
                                                </p>
                                                <div className="mt-2 flex items-center text-xs text-gray-400">
                                                    <span>{article.date}</span>
                                                    {article.read_time && (
                                                        <>
                                                            <span className="mx-2">
                                                                •
                                                            </span>
                                                            <span>
                                                                {
                                                                    article.read_time
                                                                }
                                                            </span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Link
                                                href={`/blog/${article.slug}`}
                                                target="_blank"
                                                className="text-gray-400 hover:text-gray-600"
                                                title="View Article"
                                            >
                                                <EyeIcon className="h-5 w-5" />
                                            </Link>
                                            <Link
                                                href={`/admin/articles/${article.id}/edit`}
                                                className="text-indigo-600 hover:text-indigo-900"
                                                title="Edit Article"
                                            >
                                                <PencilIcon className="h-5 w-5" />
                                            </Link>
                                            <button
                                                onClick={() =>
                                                    handleDelete(article)
                                                }
                                                className="text-red-600 hover:text-red-900"
                                                title="Delete Article"
                                            >
                                                <TrashIcon className="h-5 w-5" />
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <div className="py-12 text-center">
                            <p className="text-gray-500">No articles found</p>
                            <Link
                                href="/admin/articles/create"
                                className="mt-4 inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
                            >
                                <PlusIcon className="mr-2 h-4 w-4" />
                                Create your first article
                            </Link>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {articles.links && articles.links.length > 3 && (
                    <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                        <div className="flex flex-1 justify-between sm:hidden">
                            {articles.prev_page_url && (
                                <Link
                                    href={articles.prev_page_url}
                                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Previous
                                </Link>
                            )}
                            {articles.next_page_url && (
                                <Link
                                    href={articles.next_page_url}
                                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                    Next
                                </Link>
                            )}
                        </div>
                        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing{" "}
                                    <span className="font-medium">
                                        {articles.from}
                                    </span>{" "}
                                    to{" "}
                                    <span className="font-medium">
                                        {articles.to}
                                    </span>{" "}
                                    of{" "}
                                    <span className="font-medium">
                                        {articles.total}
                                    </span>{" "}
                                    results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm">
                                    {articles.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || "#"}
                                            className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${
                                                link.active
                                                    ? "z-10 border-indigo-500 bg-indigo-50 text-indigo-600"
                                                    : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50"
                                            } ${
                                                index === 0
                                                    ? "rounded-l-md"
                                                    : ""
                                            } ${
                                                index ===
                                                articles.links.length - 1
                                                    ? "rounded-r-md"
                                                    : ""
                                            }`}
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    ))}
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

export default Index;
