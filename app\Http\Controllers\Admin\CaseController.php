<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PortfolioCase;
use App\Models\PortfolioTag;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $cases = PortfolioCase::with('portfolioTags')->latest()->paginate(10);
        return Inertia::render('Admin/Cases/Index', compact('cases'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $portfolioTags = PortfolioTag::active()->orderBy('name')->get();

        return Inertia::render('Admin/Cases/Create', [
            'portfolioTags' => $portfolioTags,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            \Log::info('=== CASE CREATION STARTED ===');
            \Log::info('Request method: ' . $request->method());
            \Log::info('Request URL: ' . $request->fullUrl());
            \Log::info('Content-Type: ' . $request->header('Content-Type'));
            \Log::info('Request data (excluding files):', $request->except(['logo', 'images']));
            \Log::info('All request keys:', array_keys($request->all()));
            \Log::info('Has logo file: ' . ($request->hasFile('logo') ? 'YES' : 'NO'));
            \Log::info('Has images file: ' . ($request->hasFile('images') ? 'YES' : 'NO'));

            if ($request->hasFile('images')) {
                \Log::info('Images count: ' . count($request->file('images')));
                foreach ($request->file('images') as $index => $image) {
                    \Log::info("Image $index:", [
                        'name' => $image->getClientOriginalName(),
                        'size' => $image->getSize(),
                        'mime' => $image->getMimeType(),
                        'valid' => $image->isValid()
                    ]);
                }
            } else {
                \Log::warning('No images found in request');
            }

            \Log::info('Starting validation...');

            try {
                $validated = $request->validate([
                    'title' => 'required|string|max:255',
                    'company_name' => 'required|string|max:255',
                    'tags' => 'nullable|array',
                    'tags.*' => 'string|max:255',
                    'portfolio_tags' => 'nullable|array',
                    'portfolio_tags.*' => 'exists:portfolio_tags,id',
                    'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                    'images' => 'required|array|min:1',
                    'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                    'is_published' => 'nullable|boolean',
                ]);
                \Log::info('✅ Validation PASSED');
                \Log::info('Validated data keys:', array_keys($validated));
            } catch (\Illuminate\Validation\ValidationException $e) {
                \Log::error('❌ Validation FAILED');
                \Log::error('Validation errors:', $e->errors());
                throw $e;
            }

            \Log::info('Processing validated data...');

            // Set default value for is_published if not provided
            $validated['is_published'] = $validated['is_published'] ?? false;
            \Log::info('Set is_published to: ' . ($validated['is_published'] ? 'true' : 'false'));

            // Handle logo upload
            if ($request->hasFile('logo')) {
                \Log::info('Processing logo upload...');
                try {
                    $logoPath = $request->file('logo')->store('cases/logos', 'public');
                    $validated['logo'] = '/storage/' . $logoPath;
                    \Log::info('Logo uploaded successfully: ' . $validated['logo']);
                } catch (\Exception $e) {
                    \Log::error('Logo upload failed: ' . $e->getMessage());
                    throw $e;
                }
            } else {
                \Log::info('No logo to upload');
            }

            // Handle multiple image uploads
            \Log::info('Processing image uploads...');
            $imagePaths = [];
            if ($request->hasFile('images')) {
                try {
                    foreach ($request->file('images') as $index => $image) {
                        \Log::info("Uploading image $index: " . $image->getClientOriginalName());
                        $imagePath = $image->store('cases/images', 'public');
                        $imagePaths[] = '/storage/' . $imagePath;
                        \Log::info("Image $index uploaded: /storage/$imagePath");
                    }
                    \Log::info('All images uploaded successfully. Total: ' . count($imagePaths));
                } catch (\Exception $e) {
                    \Log::error('Image upload failed: ' . $e->getMessage());
                    throw $e;
                }
            } else {
                \Log::error('No images found for upload - this should not happen after validation');
            }

            $validated['images'] = $imagePaths;
            $validated['thumbnail'] = $imagePaths[0] ?? null; // First image as thumbnail
            \Log::info('Set thumbnail to: ' . ($validated['thumbnail'] ?? 'null'));

            // Generate slug
            $validated['slug'] = Str::slug($validated['title']);
            \Log::info('Generated slug: ' . $validated['slug']);

            // Extract portfolio tags before creating
            $portfolioTags = $validated['portfolio_tags'] ?? [];
            unset($validated['portfolio_tags']);
            \Log::info('Portfolio tags to sync: ' . json_encode($portfolioTags));

            \Log::info('Creating case in database...');
            try {
                $case = PortfolioCase::create($validated);
                \Log::info('✅ Case created successfully with ID: ' . $case->id);
            } catch (\Exception $e) {
                \Log::error('❌ Case creation failed: ' . $e->getMessage());
                throw $e;
            }

            // Sync portfolio tags
            if (!empty($portfolioTags)) {
                \Log::info('Syncing portfolio tags...');
                try {
                    $case->portfolioTags()->sync($portfolioTags);
                    \Log::info('✅ Portfolio tags synced successfully. Count: ' . count($portfolioTags));
                } catch (\Exception $e) {
                    \Log::error('❌ Portfolio tags sync failed: ' . $e->getMessage());
                    throw $e;
                }
            } else {
                \Log::info('No portfolio tags to sync');
            }

            \Log::info('=== CASE CREATION COMPLETED SUCCESSFULLY ===');
            return redirect()->route('admin.cases.index')->with('success', 'Case created successfully.');

        } catch (\Exception $e) {
            \Log::error('=== CASE CREATION FAILED ===');
            \Log::error('Exception type: ' . get_class($e));
            \Log::error('Exception message: ' . $e->getMessage());
            \Log::error('Exception file: ' . $e->getFile() . ':' . $e->getLine());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            // Re-throw the exception to let Laravel handle it normally
            throw $e;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PortfolioCase $case)
    {
        return inertia('Admin/Cases/Show', compact('case'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PortfolioCase $case)
    {
        $portfolioTags = PortfolioTag::active()->orderBy('name')->get();
        $case->load('portfolioTags');

        return Inertia::render('Admin/Cases/Edit', [
            'case' => $case,
            'portfolioTags' => $portfolioTags,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PortfolioCase $case)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company_name' => 'required|string|max:255',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:255',
            'portfolio_tags' => 'nullable|array',
            'portfolio_tags.*' => 'exists:portfolio_tags,id',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'existing_images' => 'nullable|array',
            'existing_images.*' => 'string',
            'is_published' => 'boolean',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($case->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $case->logo))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $case->logo));
            }

            $logoPath = $request->file('logo')->store('cases/logos', 'public');
            $validated['logo'] = '/storage/' . $logoPath;
        }

        // Handle image updates
        $finalImages = $validated['existing_images'] ?? [];

        // Add new images if uploaded
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('cases/images', 'public');
                $finalImages[] = '/storage/' . $imagePath;
            }
        }

        // Delete removed images
        if ($case->images) {
            foreach ($case->images as $oldImage) {
                if (!in_array($oldImage, $finalImages)) {
                    if (Storage::disk('public')->exists(str_replace('/storage/', '', $oldImage))) {
                        Storage::disk('public')->delete(str_replace('/storage/', '', $oldImage));
                    }
                }
            }
        }

        $validated['images'] = $finalImages;
        $validated['thumbnail'] = $finalImages[0] ?? null;

        // Update slug if title changed
        if ($validated['title'] !== $case->title) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Extract portfolio tags before updating
        $portfolioTags = $validated['portfolio_tags'] ?? [];
        unset($validated['portfolio_tags']);

        $case->update($validated);

        // Sync portfolio tags
        $case->portfolioTags()->sync($portfolioTags);

        return redirect()->route('admin.cases.index')->with('success', 'Case updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PortfolioCase $case)
    {
        // Delete associated images
        if ($case->images) {
            foreach ($case->images as $image) {
                if (Storage::disk('public')->exists(str_replace('/storage/', '', $image))) {
                    Storage::disk('public')->delete(str_replace('/storage/', '', $image));
                }
            }
        }

        // Delete logo
        if ($case->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $case->logo))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $case->logo));
        }

        $case->delete();

        return redirect()->route('admin.cases.index')->with('success', 'Case deleted successfully.');
    }
}
